/**
 * Debug Helper - مساعد التشخيص لواجهة الهاتف المحمول
 * يساعد في تشخيص مشاكل عرض الواجهة
 */

(function() {
    'use strict';

    /**
     * طباعة معلومات التشخيص
     */
    function printDebugInfo() {
        console.log('🔍 === Debug Information ===');
        
        // معلومات الجهاز
        console.log('📱 Device Info:');
        console.log(`  - User Agent: ${navigator.userAgent}`);
        console.log(`  - Screen Size: ${window.screen.width}x${window.screen.height}`);
        console.log(`  - Window Size: ${window.innerWidth}x${window.innerHeight}`);
        console.log(`  - Device Pixel Ratio: ${window.devicePixelRatio}`);
        console.log(`  - Touch Support: ${('ontouchstart' in window) ? 'Yes' : 'No'}`);
        
        // معلومات DOM
        console.log('🌐 DOM Info:');
        const body = document.body;
        const mobileView = document.querySelector('.mobile-view');
        const containerFluid = document.querySelector('.container-fluid');
        
        console.log(`  - Body Classes: ${body.className}`);
        console.log(`  - Mobile View Element: ${mobileView ? 'Found' : 'Not Found'}`);
        console.log(`  - Container Fluid Element: ${containerFluid ? 'Found' : 'Not Found'}`);
        
        if (mobileView) {
            console.log(`  - Mobile View Display: ${getComputedStyle(mobileView).display}`);
            console.log(`  - Mobile View Visibility: ${getComputedStyle(mobileView).visibility}`);
            console.log(`  - Mobile View Opacity: ${getComputedStyle(mobileView).opacity}`);
            console.log(`  - Mobile View Classes: ${mobileView.className}`);
        }
        
        if (containerFluid) {
            console.log(`  - Container Fluid Display: ${getComputedStyle(containerFluid).display}`);
            console.log(`  - Container Fluid Visibility: ${getComputedStyle(containerFluid).visibility}`);
            console.log(`  - Container Fluid Opacity: ${getComputedStyle(containerFluid).opacity}`);
        }
        
        // معلومات المستخدم
        console.log('👤 User Info:');
        const userMeta = document.querySelector('meta[name="user-role"]');
        console.log(`  - User Role: ${userMeta ? userMeta.content : 'Not Found'}`);
        
        // معلومات كاشف الأجهزة
        console.log('🔍 Device Detector Info:');
        if (window.deviceDetector) {
            console.log(`  - Device Detector: Available`);
            console.log(`  - Is Mobile: ${window.deviceDetector.isMobile}`);
            console.log(`  - Is Desktop: ${window.deviceDetector.isDesktop}`);
            console.log(`  - Device Type: ${window.deviceDetector.deviceType}`);
            console.log(`  - Device Model: ${window.deviceDetector.deviceModel}`);
        } else {
            console.log(`  - Device Detector: Not Available`);
        }
        
        console.log('🔍 === End Debug Information ===');
    }

    /**
     * فحص حالة الواجهة
     */
    function checkInterfaceState() {
        const body = document.body;
        const mobileView = document.querySelector('.mobile-view');
        const containerFluid = document.querySelector('.container-fluid');
        
        console.log('🔍 === Interface State Check ===');
        
        // فحص الكلاسات
        const isMobileDevice = body.classList.contains('mobile-device');
        const isDesktopDevice = body.classList.contains('desktop-device');
        
        console.log(`Mobile Device Class: ${isMobileDevice ? '✅' : '❌'}`);
        console.log(`Desktop Device Class: ${isDesktopDevice ? '✅' : '❌'}`);
        
        // فحص عرض العناصر
        if (mobileView) {
            const mobileDisplay = getComputedStyle(mobileView).display;
            const mobileVisible = mobileDisplay !== 'none';
            console.log(`Mobile View Visible: ${mobileVisible ? '✅' : '❌'} (${mobileDisplay})`);
        }
        
        if (containerFluid) {
            const desktopDisplay = getComputedStyle(containerFluid).display;
            const desktopVisible = desktopDisplay !== 'none';
            console.log(`Desktop View Visible: ${desktopVisible ? '✅' : '❌'} (${desktopDisplay})`);
        }
        
        // التحقق من التطابق
        if (isMobileDevice && mobileView) {
            const shouldShowMobile = getComputedStyle(mobileView).display !== 'none';
            console.log(`Mobile Interface Correct: ${shouldShowMobile ? '✅' : '❌'}`);
        }
        
        if (isDesktopDevice && containerFluid) {
            const shouldShowDesktop = getComputedStyle(containerFluid).display !== 'none';
            console.log(`Desktop Interface Correct: ${shouldShowDesktop ? '✅' : '❌'}`);
        }
        
        console.log('🔍 === End Interface State Check ===');
    }

    /**
     * إصلاح سريع للواجهة
     */
    function quickFix() {
        console.log('🔧 Applying quick fix...');
        
        const body = document.body;
        const mobileView = document.querySelector('.mobile-view');
        const containerFluid = document.querySelector('.container-fluid');
        
        // تحديد نوع الجهاز بناءً على حجم الشاشة
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // تطبيق واجهة الهاتف
            body.classList.add('mobile-device');
            body.classList.remove('desktop-device');
            
            if (mobileView) {
                mobileView.style.display = 'block';
                mobileView.style.visibility = 'visible';
                mobileView.style.opacity = '1';
            }
            
            if (containerFluid) {
                containerFluid.style.display = 'none';
                containerFluid.style.visibility = 'hidden';
            }
            
            console.log('✅ Mobile interface applied');
        } else {
            // تطبيق واجهة الكمبيوتر
            body.classList.add('desktop-device');
            body.classList.remove('mobile-device');
            
            if (containerFluid) {
                containerFluid.style.display = 'block';
                containerFluid.style.visibility = 'visible';
                containerFluid.style.opacity = '1';
            }
            
            if (mobileView) {
                mobileView.style.display = 'none';
                mobileView.style.visibility = 'hidden';
            }
            
            console.log('✅ Desktop interface applied');
        }
    }

    /**
     * مراقبة التغييرات
     */
    function startMonitoring() {
        console.log('👁️ Starting interface monitoring...');
        
        // مراقبة تغييرات الكلاسات
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    console.log('🔄 Body class changed:', document.body.className);
                    setTimeout(checkInterfaceState, 100);
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
        
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', function() {
            console.log('📏 Window resized:', window.innerWidth + 'x' + window.innerHeight);
            setTimeout(checkInterfaceState, 100);
        });
    }

    // تهيئة مساعد التشخيص
    function initDebugHelper() {
        console.log('🚀 Debug Helper initialized');
        
        // طباعة معلومات التشخيص
        setTimeout(printDebugInfo, 1000);
        
        // فحص حالة الواجهة
        setTimeout(checkInterfaceState, 1500);
        
        // بدء المراقبة
        startMonitoring();
        
        // جعل الدوال متاحة عالمياً
        window.debugHelper = {
            printDebugInfo,
            checkInterfaceState,
            quickFix
        };
        
        console.log('💡 Debug commands available: debugHelper.printDebugInfo(), debugHelper.checkInterfaceState(), debugHelper.quickFix()');
    }

    // تهيئة عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDebugHelper);
    } else {
        initDebugHelper();
    }

})();
