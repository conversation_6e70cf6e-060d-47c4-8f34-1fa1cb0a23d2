/**
 * Interface Fixer - إصلاح مشاكل عرض الواجهة
 * يضمن عرض الواجهة المناسبة بناءً على نوع الجهاز
 */

(function() {
    'use strict';

    /**
     * إصلاح عرض الواجهة
     */
    function fixInterfaceDisplay() {
        const body = document.body;
        const mobileView = document.querySelector('.mobile-view');
        const containerFluid = document.querySelector('.container-fluid');
        
        // التحقق من نوع الجهاز
        const isMobile = body.classList.contains('mobile-device');
        const isDesktop = body.classList.contains('desktop-device');
        
        console.log('🔧 Interface Fixer: Checking interface display...');
        console.log(`Device type: ${isMobile ? 'Mobile' : isDesktop ? 'Desktop' : 'Unknown'}`);
        
        if (isMobile) {
            // إصلاح واجهة الهاتف
            if (mobileView) {
                mobileView.style.display = 'block';
                mobileView.style.visibility = 'visible';
                mobileView.style.opacity = '1';
                mobileView.style.position = 'relative';
                mobileView.style.zIndex = '1';
                console.log('✅ Mobile interface fixed and displayed');
            } else {
                console.error('❌ Mobile view element not found!');
            }
            
            // إخفاء واجهة الكمبيوتر
            if (containerFluid) {
                containerFluid.style.display = 'none';
                containerFluid.style.visibility = 'hidden';
                containerFluid.style.opacity = '0';
            }
        } else if (isDesktop) {
            // إصلاح واجهة الكمبيوتر
            if (containerFluid) {
                containerFluid.style.display = 'block';
                containerFluid.style.visibility = 'visible';
                containerFluid.style.opacity = '1';
                console.log('✅ Desktop interface fixed and displayed');
            }
            
            // إخفاء واجهة الهاتف
            if (mobileView) {
                mobileView.style.display = 'none';
                mobileView.style.visibility = 'hidden';
                mobileView.style.opacity = '0';
            }
        } else {
            console.warn('⚠️ Device type not detected, applying fallback...');
            // تطبيق واجهة افتراضية بناءً على حجم الشاشة
            if (window.innerWidth <= 768) {
                body.classList.add('mobile-device');
                if (mobileView) {
                    mobileView.style.display = 'block';
                    mobileView.style.visibility = 'visible';
                    mobileView.style.opacity = '1';
                }
                if (containerFluid) {
                    containerFluid.style.display = 'none';
                    containerFluid.style.visibility = 'hidden';
                }
            } else {
                body.classList.add('desktop-device');
                if (containerFluid) {
                    containerFluid.style.display = 'block';
                    containerFluid.style.visibility = 'visible';
                    containerFluid.style.opacity = '1';
                }
                if (mobileView) {
                    mobileView.style.display = 'none';
                    mobileView.style.visibility = 'hidden';
                }
            }
        }
    }

    /**
     * مراقبة تغييرات DOM
     */
    function observeBodyChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    console.log('🔄 Body class changed, fixing interface...');
                    setTimeout(fixInterfaceDisplay, 50);
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    /**
     * تهيئة مصحح الواجهة
     */
    function initInterfaceFixer() {
        console.log('🚀 Interface Fixer initialized');
        
        // إصلاح فوري
        fixInterfaceDisplay();
        
        // مراقبة التغييرات
        observeBodyChanges();
        
        // إصلاح دوري كل 2 ثانية للتأكد
        setInterval(fixInterfaceDisplay, 2000);
        
        // إصلاح عند تغيير حجم النافذة
        window.addEventListener('resize', function() {
            setTimeout(fixInterfaceDisplay, 100);
        });
        
        // إصلاح عند تغيير الاتجاه
        window.addEventListener('orientationchange', function() {
            setTimeout(fixInterfaceDisplay, 300);
        });
    }

    // تهيئة عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initInterfaceFixer, 100);
        });
    } else {
        setTimeout(initInterfaceFixer, 100);
    }

    // تهيئة عند تحميل النافذة بالكامل
    window.addEventListener('load', function() {
        setTimeout(fixInterfaceDisplay, 200);
    });

    // جعل الدالة متاحة عالمياً للاستخدام اليدوي
    window.fixInterfaceDisplay = fixInterfaceDisplay;

})();
